﻿using System.Collections;
using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.AI;

namespace TestLE.Utilities;

public static class PlayerHelpers
{
    public static bool IsAbilityOnCooldown(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return false;
        }

        return PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, index);
    }
    
    public static string? GetAbilityName(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return null;
        }

        if (PLAYER.usingAbilityState != null)
            return PLAYER.usingAbilityState.abilityList.getAbility(index).abilityName;
        
        MelonLogger.Msg("usingAbilityState is null");
        return null;

    }

    public static void UseAbility(int index, Transform targetTransform)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, index, targetTransform.position, targetTransform, true, true, true, true);
    }

    public static void UseMovementAbility(Transform targetTransform)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        UseAbility(CURRENT_ROUTINE.MovementSkillIndex, targetTransform);
    }

    public static void UseMovementAbility(Vector3 position)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, CURRENT_ROUTINE.MovementSkillIndex, position, null, true, true, true, true);
    }

    public static void UsePortal()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UsePortalCommand(Vector3.zero, true);
    }

    public static bool UsePotion()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.healthPotion.currentCharges <= 0)
        {
            MelonLogger.Msg("No potions available");
            return false;
        }

        PLAYER.PotionKeyPressed();
        return true;
    }

    /// <summary>
    /// Will try once to move to the position.
    /// </summary>
    /// <param name="position">Position to move to</param>
    public static void MoveTo(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        MelonLogger.Msg($"[MOVEMENT_ENTRY] MoveTo called - Single movement attempt");
        Move(position);
    }

    /// <summary>
    /// Will force the player to move to the position, looping until the player is within the stoppingDistance or maxTries is reached.
    /// </summary>
    /// <param name="position">Position to move to</param>
    /// <param name="stoppingDistance">Distance to stop moving</param>
    /// <param name="maxTries">Max number of tries</param>
    /// <param name="delayBetweentries">Delay between tries</param>
    /// <returns>IEnumerator for coroutine</returns>
    public static IEnumerator MoveToForce(Vector3 position, float stoppingDistance = 1f, int maxTries = 15, float delayBetweentries = 0.3333f)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            yield break;
        }

        MelonLogger.Msg($"[MOVEMENT_ENTRY] MoveToForce called - Forced movement with retry logic");
        MelonLogger.Msg($"[MOVEMENT_ENTRY] Stopping distance: {stoppingDistance}, Max tries: {maxTries}, Delay: {delayBetweentries}s");

        for (var i = 0; i < maxTries; i++)
        {
            var currentDistance = Vector3.Distance(PLAYER.transform.position, position);
            if (currentDistance <= stoppingDistance)
            {
                MelonLogger.Msg($"[MOVEMENT_ENTRY] MoveToForce completed - Reached target (distance: {currentDistance:F2} ≤ {stoppingDistance})");
                break;
            }

            MelonLogger.Msg($"[MOVEMENT_ENTRY] MoveToForce attempt {i + 1}/{maxTries} - Distance: {currentDistance:F2}");
            Move(position);
            yield return new WaitForSeconds(delayBetweentries);
        }
    }

    private static void Move(Vector3 position)
    {
        MelonLogger.Msg($"[PATHFINDING] Move called with target position: {position}");
        MelonLogger.Msg($"[PATHFINDING] Player current position: {PLAYER.transform.position}");
        MelonLogger.Msg($"[PATHFINDING] Distance to target: {Vector3.Distance(PLAYER.transform.position, position):F2}");

        // LogNavMeshAgentState();

        if (!NavMesh.SamplePosition(position, out var hit, 10f, -1))
        {
            MelonLogger.Msg($"[PATHFINDING] Failed to find a valid move position for {position}");
            MelonLogger.Msg($"[PATHFINDING] NavMesh.SamplePosition failed with search distance 10f");

            // Try with larger search distance
            if (!NavMesh.SamplePosition(position, out hit, 50f, -1))
            {
                MelonLogger.Msg($"[PATHFINDING] NavMesh.SamplePosition also failed with search distance 50f");
                return;
            }
            else
            {
                MelonLogger.Msg($"[PATHFINDING] NavMesh.SamplePosition succeeded with larger search distance 50f");
            }
        }

        MelonLogger.Msg($"[PATHFINDING] NavMesh sampled position: {hit.position}");
        MelonLogger.Msg($"[PATHFINDING] Distance from original to sampled: {Vector3.Distance(position, hit.position):F2}");

        var path = new NavMeshPath();
        var pathCalculated = PLAYER.navMeshAgent.CalculatePath(hit.position, path);

        MelonLogger.Msg($"[PATHFINDING] Path calculation result: {pathCalculated}");
        MelonLogger.Msg($"[PATHFINDING] Path status: {path.status}");
        MelonLogger.Msg($"[PATHFINDING] Path corners count: {path.corners.Length}");

        if (path.corners.Length > 0)
        {
            MelonLogger.Msg($"[PATHFINDING] Path corners:");
            for (int i = 0; i < path.corners.Length; i++)
            {
                var corner = path.corners[i];
                var distanceFromPlayer = Vector3.Distance(PLAYER.transform.position, corner);
                MelonLogger.Msg($"[PATHFINDING]   Corner {i}: {corner} (distance: {distanceFromPlayer:F2})");
            }
        }

        // NavMesh.CalculatePath(PLAYER.transform.position, hit.position, -1, path);
        if (path.corners.Length == 0)
        {
            MelonLogger.Msg($"[PATHFINDING] Failed to find a valid path, defaulting to full path move");
            MelonLogger.Msg($"[PATHFINDING] Agent enabled: {PLAYER.navMeshAgent.enabled}");
            MelonLogger.Msg($"[PATHFINDING] Agent on NavMesh: {PLAYER.navMeshAgent.isOnNavMesh}");
            MelonLogger.Msg($"[PATHFINDING] Agent area mask: {PLAYER.navMeshAgent.areaMask}");

            // Try alternative pathfinding methods
            var alternativePath = new NavMeshPath();
            var alternativeResult = NavMesh.CalculatePath(PLAYER.transform.position, hit.position, -1, alternativePath);
            MelonLogger.Msg($"[PATHFINDING] Alternative NavMesh.CalculatePath result: {alternativeResult}");
            MelonLogger.Msg($"[PATHFINDING] Alternative path corners: {alternativePath.corners.Length}");
            MelonLogger.Msg($"[PATHFINDING] Alternative path status: {alternativePath.status}");

            // Check if target position is on NavMesh
            var targetOnNavMesh = NavMesh.SamplePosition(hit.position, out var targetHit, 1f, -1);
            MelonLogger.Msg($"[PATHFINDING] Target position on NavMesh: {targetOnNavMesh}");
            if (targetOnNavMesh)
            {
                MelonLogger.Msg($"[PATHFINDING] Target NavMesh position: {targetHit.position}");
                MelonLogger.Msg($"[PATHFINDING] Target NavMesh distance: {targetHit.distance}");
            }

            // Check if player position is on NavMesh
            var playerOnNavMesh = NavMesh.SamplePosition(PLAYER.transform.position, out var playerHit, 1f, -1);
            MelonLogger.Msg($"[PATHFINDING] Player position on NavMesh: {playerOnNavMesh}");
            if (playerOnNavMesh)
            {
                MelonLogger.Msg($"[PATHFINDING] Player NavMesh position: {playerHit.position}");
                MelonLogger.Msg($"[PATHFINDING] Player NavMesh distance: {playerHit.distance}");
            }

            InternalMove(hit.position);
            return;
        }

        // // If in monolith rest area, just move to the position
        // if (SceneManager.GetActiveScene().name == "M_Rest")
        // {
        //     InternalMove(hit.position);
        //     return;
        // }

        MelonLogger.Msg($"[PATHFINDING] Analyzing path corners for movement abilities and optimal movement:");

        foreach (var c in path.corners) // test
        {
            var distance = Vector3.Distance(PLAYER.transform.position, c);
            MelonLogger.Msg($"[PATHFINDING] Checking corner {c} at distance {distance:F2}");

            if (distance is >= 10f and <= 20f && !IsAbilityOnCooldown(CURRENT_ROUTINE!.MovementSkillIndex))
            {
                MelonLogger.Msg($"[PATHFINDING] Using movement ability to corner {c}");
                UseMovementAbility(c);
                break;
            }

            if (distance is >= 5f and <= 20f)
            {
                MelonLogger.Msg($"[PATHFINDING] Moving to intermediate corner {c}");
                InternalMove(c);
                return;
            }
        }

        // If no close corners found, default to full path move
        MelonLogger.Msg($"[PATHFINDING] No suitable intermediate corners found, moving to final destination {hit.position}");
        InternalMove(hit.position);


        // foreach (var c in PLAYER.navMeshAgent.path.corners) // test
        // {
        //     if (Vector3.Distance(PLAYER.transform.position, c) < 5f)
        //         break;
        //
        //     UseMovementAbility(c);
        //     break;
        // }

        // var directionToPlayer = (PLAYER!.transform.position - position).normalized;
        // var targetPosition = position - directionToPlayer * (PLAYER.navMeshAgent.radius + stoppingDistance);
        //
        // PLAYER.movingState.MoveToPointNoChecks(targetPosition, true);
    }

    private static void InternalMove(Vector3 position)
    {
        MelonLogger.Msg($"[PATHFINDING] InternalMove called with position: {position}");
        MelonLogger.Msg($"[PATHFINDING] Player moving state enabled: {PLAYER!.movingState.enabled}");
        MelonLogger.Msg($"[PATHFINDING] Player moving state active: {PLAYER.movingState.isActiveAndEnabled}");

        // PLAYER!.movingState.MouseClickMoveCommand(CAMERA.ScreenPointToRay(CAMERA.WorldToScreenPoint(position)), false, 1f, true, position, true);
        PLAYER!.movingState.MoveToPointNoChecks(position, true);

        MelonLogger.Msg($"[PATHFINDING] MoveToPointNoChecks executed");
    }

    private static void LogNavMeshAgentState()
    {
        var agent = PLAYER.navMeshAgent;
        MelonLogger.Msg($"[PATHFINDING] NavMesh Agent State:");
        MelonLogger.Msg($"[PATHFINDING]   Enabled: {agent.enabled}");
        MelonLogger.Msg($"[PATHFINDING]   IsOnNavMesh: {agent.isOnNavMesh}");
        MelonLogger.Msg($"[PATHFINDING]   HasPath: {agent.hasPath}");
        MelonLogger.Msg($"[PATHFINDING]   PathPending: {agent.pathPending}");
        MelonLogger.Msg($"[PATHFINDING]   PathStatus: {agent.pathStatus}");
        MelonLogger.Msg($"[PATHFINDING]   AreaMask: {agent.areaMask}");
        MelonLogger.Msg($"[PATHFINDING]   AgentTypeID: {agent.agentTypeID}");
        MelonLogger.Msg($"[PATHFINDING]   Radius: {agent.radius}");
        MelonLogger.Msg($"[PATHFINDING]   Height: {agent.height}");
        MelonLogger.Msg($"[PATHFINDING]   BaseOffset: {agent.baseOffset}");
        MelonLogger.Msg($"[PATHFINDING]   Speed: {agent.speed}");
        MelonLogger.Msg($"[PATHFINDING]   Velocity: {agent.velocity}");
        MelonLogger.Msg($"[PATHFINDING]   RemainingDistance: {agent.remainingDistance}");

        if (agent.hasPath && agent.path != null)
        {
            MelonLogger.Msg($"[PATHFINDING]   Current Path Corners: {agent.path.corners.Length}");
            MelonLogger.Msg($"[PATHFINDING]   Current Path Status: {agent.path.status}");
        }
    }

    // public static void StoreMaterials()
    // {
    //     if (STORE_MATERIALS_BUTTON == null)
    //     {
    //         MelonLogger.Msg("STORE_MATERIALS_BUTTON is null");
    //         return;
    //     }
    //
    //     STORE_MATERIALS_BUTTON.onClick.Invoke();
    // }
}
