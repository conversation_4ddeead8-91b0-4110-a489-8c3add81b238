using System.Collections;
using Il2Cpp;
using Il2CppLE.Data;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;
using Random = UnityEngine.Random;

namespace TestLE.Routines;

public class MainRoutine : IEnumerator
{
    public object? Current { get; private set; }

    // private DateTime _lastStoreMaterialsTime { get; set; } = DateTime.MinValue;
    private float _idleTime { get; set; }
    private Dictionary<Enemy, int> _enemyInactiveFailSafe { get; } = new();
    private int _currentStashTab { get; set; }


    public bool MoveNext()
    {
        if (this != MAIN_ROUTINE)
        {
            MelonLogger.Msg("MainRoutine is not the current routine!");
            return false;
        }

        if (PLAYER == null)
        {
            MelonLogger.Msg("Player is null!");
            return false;
        }

        if (CURRENT_ROUTINE == null)
        {
            MelonLogger.Msg("Current routine is null!");
            return false;
        }

        var deathScreen = FindHelpers.FindDeathScreen();
        if (deathScreen != null && deathScreen.isActiveAndEnabled)
        {
            MelonLogger.Msg("Death screen found!");
            Current = DeathAndRespawn(deathScreen);
            return true;
        }

        // // Store materials every 10 seconds
        // if (_lastStoreMaterialsTime == DateTime.MinValue || (DateTime.Now - _lastStoreMaterialsTime).TotalSeconds >= 10)
        // {
        //     PlayerHelpers.StoreMaterials();
        //     _lastStoreMaterialsTime = DateTime.Now;
        // }

        // Move to random position if idle for 5 seconds
        if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
        {
            _idleTime += Time.deltaTime;
            if (_idleTime >= 5f)
            {
                _idleTime = 0;
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] IDLE MOVEMENT - Player has been idle for 5+ seconds");
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Prevent player from staying stationary too long");
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Player velocity: {PLAYER.movingState.myAgent.velocity.magnitude:F3}");
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Idle time accumulated: {_idleTime:F1}s");

                if (UnityHelpers.RandomPointOnNavMesh(PLAYER.transform.position, 10f, out var movePos))
                {
                    MelonLogger.Msg($"[MOVEMENT_CONTEXT] Strategy: Moving to random NavMesh point within 10 units");
                    MelonLogger.Msg($"[MAIN_ROUTINE] Random NavMesh point found: {movePos}");
                    PlayerHelpers.MoveTo(movePos);
                }
                else
                {
                    var randomOffset = PLAYER.transform.position + new Vector3(Random.Range(-50, 50), 0, Random.Range(-50, 50));
                    MelonLogger.Msg($"[MOVEMENT_CONTEXT] Strategy: NavMesh sampling failed, using random offset movement");
                    MelonLogger.Msg($"[MAIN_ROUTINE] Random NavMesh point failed, using random offset: {randomOffset}");
                    PlayerHelpers.MoveTo(randomOffset);
                }

                Current = Wait(2f);
                return true;
            }
        }
        else
        {
            _idleTime = 0;
        }

        // Find monolith completed button
        var button = FindHelpers.FindMonolithCompleteButton();
        if (button != null)
        {
            Current = MonolithCompleteRoutine();
            return true;
        }

        // Find nearest enemy
        var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        if (enemy == null)
        {
            var randomOffset = PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5));
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] NO ENEMIES MOVEMENT - No enemies found within 100 units");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Explore area to find new enemies");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Strategy: Small random movement to trigger enemy spawns or find hidden enemies");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Search radius used: 100 units");
            MelonLogger.Msg($"[MAIN_ROUTINE] No mobs found! Moving to random position: {randomOffset}");
            PlayerHelpers.MoveTo(randomOffset);
            Current = Wait(1f);
            return true;
        }

        // var distance = Vector3.Distance(PLAYER.transform.position, enemy.Data.transform.position);

        // Check if we are in range of loot, if so, loot
        if (distance > 3f && GROUND_ITEMS.Count > 0)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] LOOT COLLECTION - Ground items available while enemy is distant");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Collect loot before engaging enemy");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy distance: {distance:F1} units (safe distance > 3)");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Ground items count: {GROUND_ITEMS.Count}");
            Current = HandleLoot();
            return true;
        }

        if (distance <= 3)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] CLOSE COMBAT - Enemy within melee range");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Engage enemy in close combat");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy distance: {distance:F1} units (≤ 3)");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy: {enemy.Data.Data.actorName}");
            // MelonLogger.Msg("Doing combat routine!");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        // Move to interactable and click it if it is within range
        for (var i = 0; i < INTERACTABLES.Count; i++)
        {
            var interactable = INTERACTABLES[i];
            if (interactable == null || !interactable.isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
                i--;
                continue;
            }

            var interactablePos = interactable.transform.position;
            if (Vector3.Distance(PLAYER.transform.position, interactablePos) > 20f)
                continue;

            Current = HandleInteractable(interactable);
            return true;
        }

        // Check if we are in combat range, if so, do combat routine
        if (distance <= CURRENT_ROUTINE.CombatDistance)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] COMBAT RANGE - Enemy within combat distance");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Engage enemy within optimal combat range");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy distance: {distance:F1} units");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Combat distance threshold: {CURRENT_ROUTINE.CombatDistance}");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy: {enemy.Data.Data.actorName}");
            // MelonLogger.Msg("Doing combat routine!");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        // Do good shrines before map objective
        if (GOOD_SHRINES.Count > 0)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] SHRINE PRIORITY - Good shrines available");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Activate beneficial shrines before objectives");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Available shrines count: {GOOD_SHRINES.Count}");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Strategy: Prioritize shrines over objectives for buffs");
            Current = HandleGoodShrines();
            return true;
        }

        if (MONOLITH_OBJECTIVES.Count == 0)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] NO OBJECTIVES - No monolith objectives detected");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Default to combat when no clear objectives");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Reason: Objective likely seal gate, ambush, or similar combat scenario");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Fallback strategy: Engage nearest enemy");
            // MelonLogger.Msg("Objective pulses not found, defaulting to combat routine as objective is most likely seal gate or ambush etc.");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        if (objective == null)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] NULL OBJECTIVE - Objective list has items but first is null");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Clean up invalid objective and default to combat");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Action: Removing null objective from list");
            // MelonLogger.Msg("Objective is null!");
            Current = CombatRoutine(enemy, distance);
            MONOLITH_OBJECTIVES.RemoveAt(0);
            return true;
        }

        var enemyObjective = objective.GetEnemyObjective();
        if (enemyObjective != null)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] ENEMY OBJECTIVE - Monolith objective is an enemy target");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Move to and eliminate specific objective enemy");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Target enemy: {enemyObjective.name}");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Objective position: {enemyObjective.transform.position}");
            // MelonLogger.Msg("Objective is enemy!");
            Current = MoveToMonolithObjective_Enemy(enemyObjective);
            return true;
        }

        var clickObjective = objective.GetClickObjective();
        if (clickObjective != null)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] CLICK OBJECTIVE - Monolith objective is an interactable object");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Move to and interact with objective object");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Target object: {clickObjective.name}");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Objective position: {clickObjective.transform.position}");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Interaction range: {clickObjective.interactionRange}");
            // MelonLogger.Msg("Objective is clickable!");
            Current = MoveToMonolithObjective_Click(clickObjective);
            return true;
        }

        MelonLogger.Msg($"[MOVEMENT_CONTEXT] INVALID OBJECTIVE - Objective exists but has no valid target");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Default to combat when objective is malformed");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Fallback strategy: Engage nearest enemy while objective resolves");
        // MelonLogger.Msg("Objective not valid, defaulting to combat routine.");
        Current = CombatRoutine(enemy, distance);
        return true;
    }

    public void Reset()
    {
        Current = null;
    }

    private IEnumerator CombatRoutine(Enemy enemy, float distance)
    {
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] COMBAT ROUTINE ENTRY");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy: {enemy.Data.Data.actorName}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Distance: {distance:F1} units");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy active: {enemy.Data.gameObject.active}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy enabled: {enemy.Data.isActiveAndEnabled}");

        // if (distance <= 1f && !enemy.Data.isActiveAndEnabled)
        if (distance <= 3f && enemy.Data.Data.actorName != "Exiled Mage" && (!enemy.Data.actorSync.gameObject.active || !enemy.Data.isActiveAndEnabled))
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] ENEMY CLEANUP - Enemy too close and inactive");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Remove invalid enemy to prevent stuck behavior");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy distance: {distance:F1} (≤ 3)");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Enemy type: {enemy.Data.Data.actorName}");
            MelonLogger.Msg("Enemy is too close and not active!");
            enemy.RemoveEnemy();
            yield break;
        }

        var enemyTransform = enemy.Data.transform;
        if (enemy.Data.gameObject.active)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] ACTIVE ENEMY COMBAT - Enemy is active, delegating to routine");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Execute class-specific combat routine");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Routine: {CURRENT_ROUTINE!.GetType().Name}");
            yield return CURRENT_ROUTINE!.Run(enemy, enemyTransform, distance);
        }
        else
        {
            if (distance <= 3f)
            {
                _enemyInactiveFailSafe[enemy] = _enemyInactiveFailSafe.GetValueOrDefault(enemy) + 1;
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] INACTIVE ENEMY FAILSAFE - Enemy inactive at close range");
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Track inactive enemy attempts to prevent infinite loops");
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Attempts: {_enemyInactiveFailSafe[enemy]}/10");

                if (_enemyInactiveFailSafe[enemy] >= 10)
                {
                    MelonLogger.Msg($"[MOVEMENT_CONTEXT] ENEMY REMOVAL - Max inactive attempts reached");
                    MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Remove persistently inactive enemy");
                    MelonLogger.Msg("Enemy is inactive for 10 tries, removing enemy!");
                    enemy.RemoveEnemy();
                    _enemyInactiveFailSafe.Remove(enemy);
                    yield break;
                }
            }

            MelonLogger.Msg($"[MOVEMENT_CONTEXT] INACTIVE ENEMY APPROACH - Moving to inactive enemy");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Approach inactive enemy to trigger activation or combat");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Strategy: Move to enemy position and wait for activation");
            MelonLogger.Msg($"[MAIN_ROUTINE] Enemy inactive, moving to enemy position: {enemyTransform.position}");
            PlayerHelpers.MoveTo(enemyTransform.position);
            yield return new WaitForSeconds(0.3333f);
        }
    }

    private IEnumerator MonolithCompleteRoutine()
    {
        // Stop player by moving to the current position
        PlayerHelpers.MoveTo(PLAYER.transform.position);
        yield return new WaitForSeconds(1f);

        // Make portal
        PlayerHelpers.UsePortal();
        yield return new WaitForSeconds(2f);

        // Find portal
        var portal = FindHelpers.FindMonolithPortal();
        if (portal == null)
        {
            MelonLogger.Msg("Portal not found!");
            yield break;
        }

        // Wait for all ground items to be spawned
        if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
            yield return new WaitForSeconds(1f);

        // Loot all ground items before moving to portal
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Move to portal
        yield return PlayerHelpers.MoveToForce(portal.transform.position);

        // Click on portal
        portal.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Clear enemies
        // ENEMIES.Clear();
        // MONOLITH_OBJECTIVES.Clear();
        // GROUND_ITEMS.Clear();
        ResetGlobals();
        _enemyInactiveFailSafe.Clear();

        // Click on reward chest
        var chest = FindHelpers.FindMonolithCompleteRewardChest();
        if (chest.obj != null && chest.isActive)
        {
            yield return PlayerHelpers.MoveToForce(chest.obj.transform.position);

            chest.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Chest not found!");
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Click on reward rock
        var rock = FindHelpers.FindMonolithCompleteRewardRock();
        if (rock.obj != null && rock.isActive)
        {
            yield return PlayerHelpers.MoveToForce(rock.obj.transform.position);

            rock.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Rock not found!");
        }

        // Wait for 1 second, to allow the loot to finish spawning
        yield return new WaitForSeconds(1f);

        // Move and collect XP tomes
        var tomes = FindHelpers.FindGroundXPTomes();
        foreach (var t in tomes)
        {
            yield return PlayerHelpers.MoveToForce(t.transform.position);
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Store all materials
        // PlayerHelpers.StoreMaterials();

        // Find stash opener, and move to it, then stash all items
        yield return StashItems();

        // Move to next monolith
        yield return GoNextMonolith();
        MelonLogger.Msg("Monolith completed!");
    }

    private static IEnumerator GoNextMonolith()
    {
        // Find monolith stone
        var stone = FindHelpers.FindMonolithStone();
        if (stone == null)
        {
            MelonLogger.Msg("Stone not found!");
            yield break;
        }

        // Move to monolith stone
        yield return PlayerHelpers.MoveToForce(stone.transform.position);

        // Click on monolith stone
        stone.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Find monolith islands
        var islands = FindHelpers.FindMonolithIslands();
        if (islands.Count == 0)
        {
            MelonLogger.Msg("Islands not found!");
            yield break;
        }

        // Click on next island
        foreach (var (_, ui) in islands)
        {
            if (ui.island.completed)
                continue;

            if (ui.island.islandType is not EchoWebIsland.IslandType.Normal and not EchoWebIsland.IslandType.Arena and not EchoWebIsland.IslandType.Beacon)
                continue;

            var hasConnectionWithComepletedIsland = false;
            foreach (var c in ui.island.connectedHexes)
            {
                var connectedIsland = islands.GetValueOrDefault(c);
                if (connectedIsland == null)
                    continue;

                if (!connectedIsland.island.completed)
                    continue;

                hasConnectionWithComepletedIsland = true;
                break;
            }

            if (!hasConnectionWithComepletedIsland)
                continue;

            MelonLogger.Msg($"Next monolith reward: {(ui.rewards.Count > 0 ? ui.rewards.getAtIndexOrFirst(0).rewardType : "NULL")}");
            ui.rightClicked();
            break;
        }

        while (SceneManager.GetActiveScene().name == "M_Rest")
            yield return new WaitForSeconds(1f);

        yield return new WaitForSeconds(1f);
        yield return CURRENT_ROUTINE!.OnNewArea();
    }

    private static IEnumerator DeathAndRespawn(DeathScreen deathScreen)
    {
        deathScreen.NormalRespawnClick();
        yield return new WaitForSeconds(1f);

        yield return GoNextMonolith();
    }

    private static IEnumerator HandleLoot()
    {
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] LOOT HANDLING - Processing ground items");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Collect valuable items from the ground");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Total ground items: {GROUND_ITEMS.Count}");
        MelonLogger.Msg("Handling loot!");

        var groundItem = GROUND_ITEMS.FirstOrDefault();
        if (groundItem == null)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] NULL GROUND ITEM - First item in list is null");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Clean up invalid item reference");
            MelonLogger.Msg("Ground item is null!");
            if (GROUND_ITEMS.Count > 0)
                GROUND_ITEMS.RemoveAt(0);

            yield break;
        }

        var itemPosition = groundItem.Label switch
        {
            GroundItemLabel item => item.visuals.transform.position,
            GroundGoldLabel gold => gold.visuals.transform.position,
            GroundPotionLabel potion => potion.visuals.transform.position,
            AncientBoneLabel bone => bone.visuals.transform.position,
            _ => groundItem.Label.transform.position
        };

        MelonLogger.Msg($"[MOVEMENT_CONTEXT] LOOT APPROACH - Moving to ground item");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Approach item for pickup");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Item type: {groundItem.GetType().Name}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Label type: {groundItem.Label.GetType().Name}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Item position: {itemPosition}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Distance to item: {Vector3.Distance(PLAYER.transform.position, itemPosition):F1} units");
        MelonLogger.Msg("Moving to ground item!");
        yield return groundItem.MoveToItem();
        groundItem.Pickup();
    }

    private static IEnumerator HandleInteractable(WorldObjectClickListener interactable)
    {
        yield return PlayerHelpers.MoveToForce(interactable.transform.position, interactable.interactionRange * 0.8f);
        // yield return PlayerHelpers.MoveToForce(MathHelpers.GetPositionBetweenTargetAndPlayer(interactable.transform.position, interactable.interactionRange));

        INTERACTABLES.Remove(interactable);
        interactable.ObjectClick(PLAYER.gameObject, true);
    }

    private static IEnumerator HandleGoodShrines()
    {
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] SHRINE HANDLING - Processing good shrines");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Activate beneficial shrines for buffs");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Initial shrine count: {GOOD_SHRINES.Count}");

        for (var i = 0; i < GOOD_SHRINES.Count; i++)
        {
            var s = GOOD_SHRINES[i];
            if (s == null)
            {
                MelonLogger.Msg($"[MOVEMENT_CONTEXT] Removing null shrine at index {i}");
                GOOD_SHRINES.RemoveAt(i);
                i--;
            }
        }

        var playerPos = PLAYER.transform.position;
        GOOD_SHRINES.Sort((v1, v2) => Vector3.Distance(playerPos, v1!.transform.position).CompareTo(Vector3.Distance(playerPos, v2!.transform.position)));

        var shrine = GOOD_SHRINES.First();
        var shrineDistance = Vector3.Distance(playerPos, shrine!.transform.position);
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] SHRINE APPROACH - Moving to nearest shrine");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Approach shrine for activation");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Shrine name: {shrine.name}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Shrine distance: {shrineDistance:F1} units");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Interaction range: {shrine.interactionRange}");
        MelonLogger.Msg($"[MAIN_ROUTINE] Moving to shrine at position: {shrine!.transform.position}");
        PlayerHelpers.MoveTo(shrine!.transform.position);
        yield return new WaitForSeconds(0.3333f);

        // ReSharper disable once Unity.InefficientPropertyAccess
        var distance = Vector3.Distance(PLAYER.transform.position, shrine.transform.position);
        if (distance <= shrine.interactionRange)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] SHRINE ACTIVATION - Within interaction range, activating shrine");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Final distance: {distance:F1} units (≤ {shrine.interactionRange})");
            shrine.ObjectClick(PLAYER.gameObject, true);
            GOOD_SHRINES.Remove(shrine);
        }
        else
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] SHRINE TOO FAR - Not within interaction range");
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] Final distance: {distance:F1} units (> {shrine.interactionRange})");
        }
    }

    private static IEnumerator MoveToMonolithObjective_Enemy(ActorVisuals enemyObjective)
    {
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] ENEMY OBJECTIVE APPROACH - Moving to objective enemy");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Approach specific enemy required for monolith completion");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Target: {enemyObjective.name}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Position: {enemyObjective.transform.position}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Distance: {Vector3.Distance(PLAYER.transform.position, enemyObjective.transform.position):F1} units");
        MelonLogger.Msg($"[MAIN_ROUTINE] Moving to enemy objective at position: {enemyObjective.transform.position}");
        PlayerHelpers.MoveTo(enemyObjective.transform.position);
        yield return new WaitForSeconds(0.3333f);
    }

    private static IEnumerator MoveToMonolithObjective_Click(WorldObjectClickListener clickObjective)
    {
        var objectiveTransform = clickObjective.transform;
        var objectivePosition = objectiveTransform.position;
        var initialDistance = Vector3.Distance(PLAYER.transform.position, objectivePosition);

        MelonLogger.Msg($"[MOVEMENT_CONTEXT] CLICK OBJECTIVE APPROACH - Moving to interactable objective");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Purpose: Approach and interact with monolith objective object");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Target: {clickObjective.name}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Position: {objectivePosition}");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Initial distance: {initialDistance:F1} units");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Interaction range: {clickObjective.interactionRange}");

        // var directionToPlayer = (PLAYER!.transform.position - objectivePosition).normalized;
        // var targetPosition = objectivePosition - directionToPlayer * (clickObjective.interactionRange * 0.8f);

        // PlayerHelpers.MoveTo(targetPosition);
        MelonLogger.Msg($"[MAIN_ROUTINE] Moving to click objective at position: {objectivePosition}");
        PlayerHelpers.MoveTo(objectivePosition);
        yield return new WaitForSeconds(0.3333f);

        // Disable the warning because we waited 0.3333 seconds, which means we can't use the cached position of the objective, only the transform component
        // ReSharper disable once Unity.InefficientPropertyAccess
        var distance = Vector3.Distance(PLAYER.transform.position, objectiveTransform.position);
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] OBJECTIVE INTERACTION CHECK - Checking if within interaction range");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Final distance: {distance:F1} units");
        MelonLogger.Msg($"[MOVEMENT_CONTEXT] Required range: ≤ {clickObjective.interactionRange}");

        if (distance <= clickObjective.interactionRange)
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] OBJECTIVE INTERACTION - Within range, clicking objective");
            clickObjective.ObjectClick(PLAYER.gameObject, true);
        }
        else
        {
            MelonLogger.Msg($"[MOVEMENT_CONTEXT] OBJECTIVE TOO FAR - Not within interaction range, click skipped");
        }
    }

    private static IEnumerator Wait(float duration)
    {
        yield return new WaitForSeconds(duration);
    }

    private IEnumerator StashItems()
    {
        // Find stash opener, and move to it
        var stashOpener = FindHelpers.FindStashOpener();
        if (stashOpener == null)
        {
            MelonLogger.Msg("StashOpener not found!");
            yield break;
        }

        yield return PlayerHelpers.MoveToForce(stashOpener.transform.position);
        stashOpener.OnUse();

        yield return new WaitForSeconds(0.1f);

        // Find inventory UI, and move all items to stash
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("InventoryUI not found!");
            yield break;
        }

        if (inventoryUI.container == null)
        {
            MelonLogger.Msg("inventoryUI.container not found!");
            yield break;
        }

        var content = inventoryUI.container.GetContent();
        if (content == null)
        {
            MelonLogger.Msg("inventoryUI.container.GetContent() is null!");
            yield break;
        }

        var stashNavigable = FindHelpers.FindStashNavigable();
        if (stashNavigable == null)
        {
            MelonLogger.Msg("StashNavigable not found!");
            yield break;
        }

        var stashTabUI = FindHelpers.FindStashTabUI();
        if (stashTabUI == null)
        {
            MelonLogger.Msg("StashTabUI not found!");
            yield break;
        }

        yield return SelectCurrentStashTab(stashNavigable, stashTabUI);

        while (content.Count > 0)
        {
            var items = new List<ItemContainerEntry>();
            foreach (var i in content)
            {
                if (i == null)
                {
                    MelonLogger.Msg("Item is null!");
                    continue;
                }

                items.Add(i);
            }

            foreach (var i in items)
                inventoryUI.TryQuickMove(i.Position);

            yield return new WaitForSeconds(0.1f);

            if (content.Count == 0)
                break;

            MelonLogger.Msg("Stashing items failed, trying next stash tab!");
            yield return SelectNextStashTab(stashNavigable, stashTabUI);
        }

        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectCurrentStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI) // TODO: Set the stash tab index 0 first
    {
        stashTabUI.SwitchToTab(0);
        stashNavigable.ResetIndex();
        for (var i = 0; i < _currentStashTab; i++)
            // stashTabUI.NextTab();
            stashNavigable.IndexToRight();

        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectNextStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI)
    {
        _currentStashTab++;
        // stashTabUI.NextTab();
        stashNavigable.IndexToRight();
        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }
}
